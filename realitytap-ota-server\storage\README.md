# RealityTap OTA 存储目录

## 目录结构

- `releases/` - 发布文件存储
  - `stable/` - 稳定版本
  - `beta/` - 测试版本
  - `alpha/` - 开发版本
- `metadata/` - 元数据文件（遗留目录，用于清理）
  - `versions/` - 版本元数据文件（待清理）
- `database/` - SQLite 数据库文件
- `logs/` - 日志文件
- `temp/` - 临时文件
- `backup/` - 备份文件

## 使用说明

1. 将发布文件放入对应的 `releases/` 子目录
2. 使用管理界面或 API 上传和管理版本信息
3. 所有版本和渠道配置现在存储在 SQLite 数据库中

## 注意事项

- 版本信息现在存储在数据库中，不再使用 JSON 文件
- 定期清理临时文件和旧版本
- 定期备份数据库文件
