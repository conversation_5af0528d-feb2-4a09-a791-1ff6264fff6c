<template>
  <div class="batch-upload">
    <div class="upload-header">
      <h3>批量上传 (MSI + 签名文件)</h3>
      <p class="upload-description">
        同时上传安装包文件(.msi)和对应的签名文件(.sig)，系统会自动关联这两个文件。
      </p>
    </div>

    <div class="upload-form">
      <!-- 文件选择区域 -->
      <div class="file-selection">
        <div class="file-input-group">
          <label for="installer-file">安装包文件 (.msi)</label>
          <input
            id="installer-file"
            type="file"
            accept=".msi,.exe,.dmg,.pkg"
            @change="handleInstallerFileChange"
            :disabled="uploading"
          />
          <div v-if="installerFile" class="file-info">
            <span class="file-name">{{ installerFile.name }}</span>
            <span class="file-size">({{ formatFileSize(installerFile.size) }})</span>
          </div>
        </div>

        <div class="file-input-group">
          <label for="signature-file">签名文件 (.sig) - 可选</label>
          <input
            id="signature-file"
            type="file"
            accept=".sig"
            @change="handleSignatureFileChange"
            :disabled="uploading"
          />
          <div v-if="signatureFile" class="file-info">
            <span class="file-name">{{ signatureFile.name }}</span>
            <span class="file-size">({{ formatFileSize(signatureFile.size) }})</span>
          </div>
        </div>
      </div>

      <!-- 版本信息表单 -->
      <div class="metadata-form">
        <h4>版本信息</h4>
        <div class="form-grid">
          <div class="form-group">
            <label for="version">版本号</label>
            <input
              id="version"
              v-model="metadata.version"
              type="text"
              placeholder="1.0.0"
              :disabled="uploading"
              required
            />
          </div>

          <div class="form-group">
            <label for="platform">平台</label>
            <select id="platform" v-model="metadata.platform" :disabled="uploading" required>
              <option value="windows">Windows</option>
              <option value="macos">macOS</option>
              <option value="linux">Linux</option>
            </select>
          </div>

          <div class="form-group">
            <label for="architecture">架构</label>
            <select id="architecture" v-model="metadata.architecture" :disabled="uploading" required>
              <option value="x86_64">x86_64</option>
              <option value="aarch64">aarch64</option>
              <option value="x86">x86</option>
            </select>
          </div>

          <div class="form-group">
            <label for="channel">发布渠道</label>
            <select id="channel" v-model="metadata.channel" :disabled="uploading">
              <option value="stable">稳定版</option>
              <option value="beta">测试版</option>
              <option value="alpha">开发版</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="release-notes">发布说明</label>
          <textarea
            id="release-notes"
            v-model="metadata.releaseNotes"
            placeholder="描述此版本的更新内容，支持 Markdown 格式..."
            :disabled="uploading"
            rows="4"
          ></textarea>
          <small class="form-hint">
            💡 支持 Markdown 格式：**粗体**、*斜体*、`代码`、[链接](url)、列表等
          </small>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="metadata.isForced"
              :disabled="uploading"
            />
            强制更新
          </label>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div class="upload-actions">
        <button
          type="button"
          @click="startBatchUpload"
          :disabled="!canUpload || uploading"
          class="upload-btn"
        >
          <span v-if="uploading">上传中...</span>
          <span v-else>开始上传</span>
        </button>

        <button
          v-if="uploading"
          type="button"
          @click="cancelUpload"
          class="cancel-btn"
        >
          取消上传
        </button>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploadProgress" class="upload-progress">
        <h4>上传进度</h4>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: uploadProgress.overallProgress.percentage + '%' }"
          ></div>
        </div>
        <div class="progress-info">
          <span>{{ uploadProgress.overallProgress.percentage }}%</span>
          <span>
            {{ uploadProgress.overallProgress.completedFiles }} / {{ uploadProgress.overallProgress.totalFiles }} 文件
          </span>
          <span>{{ formatFileSize(uploadProgress.overallProgress.uploadedBytes) }} / {{ formatFileSize(uploadProgress.overallProgress.totalBytes) }}</span>
        </div>

        <!-- 文件详细进度 -->
        <div class="file-progress-list">
          <div
            v-for="fileStatus in uploadProgress.files"
            :key="fileStatus.filename"
            class="file-progress-item"
          >
            <div class="file-progress-header">
              <span class="filename">{{ fileStatus.filename }}</span>
              <span class="file-type-badge" :class="fileStatus.fileType">
                {{ fileStatus.fileType === 'installer' ? '安装包' : '签名' }}
              </span>
              <span class="status" :class="fileStatus.status">{{ getStatusText(fileStatus.status) }}</span>
            </div>
            <div v-if="fileStatus.totalBytes > 0" class="file-progress-bar">
              <div
                class="file-progress-fill"
                :style="{ width: (fileStatus.uploadedBytes / fileStatus.totalBytes * 100) + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <h4>上传失败</h4>
        <p>{{ error }}</p>
      </div>

      <!-- 成功信息 -->
      <div v-if="success" class="success-message">
        <h4>上传成功</h4>
        <p>文件已成功上传并处理完成。</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { adminApi } from '@/api/admin'
import type {
  BatchUploadInitRequest,
  BatchUploadProgress,
  FileUploadRequest,
  UploadStatus
} from '@/types/upload'
import { calculateFileHash } from '@/utils/crypto'

// 响应式数据
const installerFile = ref<File | null>(null)
const signatureFile = ref<File | null>(null)
const uploading = ref(false)
const uploadProgress = ref<BatchUploadProgress | null>(null)
const error = ref<string | null>(null)
const success = ref(false)
const sessionId = ref<string | null>(null)

// 版本元数据
const metadata = ref<FileUploadRequest>({
  version: '',
  platform: 'windows',
  architecture: 'x86_64',
  channel: 'stable',
  releaseNotes: '',
  isForced: false
})

// 计算属性
const canUpload = computed(() => {
  return installerFile.value && metadata.value.version.trim() !== ''
})

// 文件处理方法
const handleInstallerFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    installerFile.value = target.files[0]
    // 自动从文件名解析版本信息
    parseVersionFromFilename(target.files[0].name)
  }
}

const handleSignatureFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    signatureFile.value = target.files[0]
  }
}

// 从文件名解析版本信息
const parseVersionFromFilename = (filename: string) => {
  const versionMatch = filename.match(/(\d+\.\d+\.\d+)/)
  if (versionMatch) {
    metadata.value.version = versionMatch[1]
  }

  if (/windows|win|\.exe|\.msi/i.test(filename)) {
    metadata.value.platform = 'windows'
  } else if (/macos|mac|osx|\.dmg|\.pkg/i.test(filename)) {
    metadata.value.platform = 'macos'
  } else if (/linux|\.deb|\.rpm|\.appimage/i.test(filename)) {
    metadata.value.platform = 'linux'
  }

  if (/x86_64|x64|amd64/i.test(filename)) {
    metadata.value.architecture = 'x86_64'
  } else if (/aarch64|arm64/i.test(filename)) {
    metadata.value.architecture = 'aarch64'
  } else if (/x86|i386|i686/i.test(filename)) {
    metadata.value.architecture = 'x86'
  }

  if (/alpha/i.test(filename)) {
    metadata.value.channel = 'alpha'
  } else if (/beta/i.test(filename)) {
    metadata.value.channel = 'beta'
  }
}



// 开始批量上传
const startBatchUpload = async () => {
  if (!installerFile.value) return

  try {
    uploading.value = true
    error.value = null
    success.value = false

    // 准备文件信息
    const files = []
    
    // 添加安装包文件
    files.push({
      filename: installerFile.value.name,
      fileSize: installerFile.value.size,
      fileHash: await calculateFileHash(installerFile.value),
      fileType: 'installer' as const
    })

    // 添加签名文件（如果有）
    if (signatureFile.value) {
      files.push({
        filename: signatureFile.value.name,
        fileSize: signatureFile.value.size,
        fileHash: await calculateFileHash(signatureFile.value),
        fileType: 'signature' as const
      })
    }

    // 初始化批量上传
    const initRequest: BatchUploadInitRequest = {
      files,
      metadata: metadata.value
    }

    const initResponse = await adminApi.initBatchUpload(initRequest)
    if (!initResponse.data) {
      throw new Error('初始化批量上传失败：响应数据为空')
    }
    sessionId.value = initResponse.data.sessionId

    // 准备上传的文件数组
    const filesToUpload = [installerFile.value]
    if (signatureFile.value) {
      filesToUpload.push(signatureFile.value)
    }

    // 上传文件
    await adminApi.uploadBatchFiles(sessionId.value, filesToUpload, (_progress) => {
      // 这里可以处理上传进度
      // 暂时不处理进度，使用下划线前缀避免 TypeScript 警告
    })

    // 完成上传
    await adminApi.completeBatchUpload(sessionId.value)

    success.value = true
    uploadProgress.value = null
  } catch (err: any) {
    error.value = err.message || '上传失败'
  } finally {
    uploading.value = false
  }
}

// 取消上传
const cancelUpload = () => {
  uploading.value = false
  uploadProgress.value = null
  sessionId.value = null
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取状态文本
const getStatusText = (status: UploadStatus): string => {
  const statusMap = {
    initializing: '初始化',
    uploading: '上传中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    merging: '处理中'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.batch-upload {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.upload-header {
  margin-bottom: 30px;
}

.upload-header h3 {
  color: #333;
  margin-bottom: 10px;
}

.upload-description {
  color: #666;
  line-height: 1.5;
}

.file-selection {
  margin-bottom: 30px;
}

.file-input-group {
  margin-bottom: 20px;
}

.file-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.file-input-group input[type="file"] {
  width: 100%;
  padding: 8px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background: #fafafa;
}

.file-info {
  margin-top: 5px;
  font-size: 14px;
  color: #666;
}

.file-name {
  font-weight: 500;
}

.file-size {
  margin-left: 10px;
}

.metadata-form {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.metadata-form h4 {
  margin-bottom: 20px;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.upload-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.upload-btn,
.cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-btn {
  background: #007bff;
  color: white;
}

.upload-btn:hover:not(:disabled) {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background: #c82333;
}

.upload-progress {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.file-progress-list {
  margin-top: 20px;
}

.file-progress-item {
  background: white;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
}

.file-progress-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.filename {
  font-weight: 500;
  flex: 1;
}

.file-type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.file-type-badge.installer {
  background: #e3f2fd;
  color: #1976d2;
}

.file-type-badge.signature {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
}

.status.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status.uploading {
  background: #fff3e0;
  color: #f57c00;
}

.status.failed {
  background: #ffebee;
  color: #c62828;
}

.file-progress-bar {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.file-progress-fill {
  height: 100%;
  background: #28a745;
  transition: width 0.3s ease;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
}

.form-hint {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  font-style: italic;
}
</style>
