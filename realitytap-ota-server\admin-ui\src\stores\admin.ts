import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { adminApi } from '@/api/admin'
import type { SystemStats, VersionInfo } from '@/api/types'

export const useAdminStore = defineStore('admin', () => {
  // 状态
  const stats = ref<SystemStats | null>(null)
  const versions = ref<VersionInfo[]>([])
  const loading = ref(false)
  const uploading = ref(false)

  // 计算属性
  const totalVersions = computed(() => versions.value.length)
  const systemUptime = computed(() => {
    if (!stats.value) return '0分钟'
    const uptime = stats.value.system.uptime
    const hours = Math.floor(uptime / 3600)
    const minutes = Math.floor((uptime % 3600) / 60)
    return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`
  })

  // 获取系统统计信息
  const fetchStats = async () => {
    loading.value = true
    try {
      const response = await adminApi.getStats()
      if (response.success && response.data) {
        stats.value = response.data
      }
    } catch (error) {
      console.error('Fetch stats error:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取版本列表
  const fetchVersions = async () => {
    loading.value = true
    try {
      const response = await adminApi.getVersions()
      if (response.success && response.data) {
        versions.value = response.data
      }
    } catch (error) {
      console.error('Fetch versions error:', error)
    } finally {
      loading.value = false
    }
  }

  // 注意：简单上传功能已弃用，请使用批量上传组件
  // 这个方法保留是为了向后兼容，但实际上应该使用 ChunkFileUpload 组件
  const uploadFile = async (_file: File, _onProgress?: (progress: number) => void) => {
    console.warn('uploadFile 方法已弃用，请使用批量上传组件')
    return {
      success: false,
      message: '简单上传功能已弃用，请使用批量上传功能',
    }
  }

  // 删除版本
  const deleteVersion = async (versionId: string) => {
    try {
      const response = await adminApi.deleteVersion(versionId)
      if (response.success) {
        // 从本地状态中移除
        versions.value = versions.value.filter(v => v.id !== versionId)
        return { success: true }
      } else {
        throw new Error(response.error?.message || '删除失败')
      }
    } catch (error: any) {
      console.error('Delete version error:', error)
      return {
        success: false,
        message: error.message || '删除失败',
      }
    }
  }

  // 切换强制更新状态
  const toggleForceUpdate = async (filename: string, isForced: boolean) => {
    try {
      const response = await adminApi.toggleForceUpdate(filename, isForced)
      if (response.success) {
        // 更新本地状态中对应版本的强制更新状态
        const version = versions.value.find(v => v.filename === filename)
        if (version) {
          version.isForced = isForced
        }
        return { success: true }
      } else {
        throw new Error(response.error?.message || '更新失败')
      }
    } catch (error: any) {
      console.error('Toggle force update error:', error)
      return {
        success: false,
        message: error.message || '更新失败',
      }
    }
  }

  // 清理无效版本
  const cleanupInvalidVersions = async () => {
    try {
      const response = await adminApi.cleanupInvalidVersions()
      return response
    } catch (error: any) {
      console.error('Cleanup invalid versions error:', error)
      return {
        success: false,
        message: error.message || '清理失败',
      }
    }
  }

  // 刷新所有数据
  const refreshData = async () => {
    await Promise.all([
      fetchStats(),
      fetchVersions(),
    ])
  }

  return {
    // 状态
    stats: computed(() => stats.value),
    versions: computed(() => versions.value),
    loading: computed(() => loading.value),
    uploading: computed(() => uploading.value),
    
    // 计算属性
    totalVersions,
    systemUptime,
    
    // 方法
    fetchStats,
    fetchVersions,
    uploadFile,
    deleteVersion,
    toggleForceUpdate,
    cleanupInvalidVersions,
    refreshData,
  }
})
