import { logger } from '@/utils/logger.util';

// 数据库服务
import { versionDatabaseService } from './version-db.service';
import { configDatabaseService } from './config-db.service';
import { statsDatabaseService } from './stats-db.service';

// 服务接口定义
export interface IVersionService {
  checkForUpdates(request: any): Promise<any>;
  getAvailableVersions(): Promise<any>;
  getChannelInfo(): Promise<any>;
  updateVersionsConfig(config: any): Promise<void>;
  updateChannelsConfig(config: any): Promise<void>;
  clearCache(): void;
}

export interface IConfigService {
  // 基础配置操作
  getConfig(): Promise<any>;
  updateConfig(config: any, userId?: string, reason?: string): Promise<any>;
  resetConfig(userId?: string, reason?: string): Promise<any>;

  // 配置验证和模式
  validateConfig(config: any): Promise<{ isValid: boolean; errors: string[] }>;
  getConfigSchema(): any;
  getDefaultConfig(): any;

  // 配置历史
  getConfigHistory(limit?: number): Promise<any>;

  // 单个配置值操作
  getConfigValue<T>(key: string, defaultValue?: T): Promise<T | undefined>;
  setConfigValue(key: string, value: any, userId?: string, reason?: string): Promise<void>;

  // 缓存管理
  clearCache(): void;
}

export interface IStatsService {
  recordDownload(downloadInfo: any): Promise<void>;
  getDownloadStats(query?: any): Promise<any>;
  getDownloadRecords(query?: any): Promise<any>;
  getSummaryStats(): Promise<any>;
  cleanupOldStats(daysToKeep: number): Promise<void>;
  clearCache(): void;
}

/**
 * 数据库服务管理器
 * 提供数据库服务的初始化和健康检查功能
 */
export class DatabaseServiceManager {
  private static instance: DatabaseServiceManager;

  private constructor() {}

  public static getInstance(): DatabaseServiceManager {
    if (!DatabaseServiceManager.instance) {
      DatabaseServiceManager.instance = new DatabaseServiceManager();
    }
    return DatabaseServiceManager.instance;
  }

  /**
   * 检查数据库是否可用
   */
  public async isDatabaseAvailable(): Promise<boolean> {
    try {
      const { dbConnection } = await import('@/database/connection');
      return await dbConnection.healthCheck();
    } catch (error) {
      logger.error('Database availability check failed', { error });
      return false;
    }
  }

  /**
   * 初始化数据库服务
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing database services');

      // 初始化数据库连接（包含所有表和数据的创建）
      const { dbConnection } = await import('@/database/connection');
      await dbConnection.initialize();

      logger.info('Database services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services', { error });
      throw new Error(`Service initialization failed: ${error}`);
    }
  }
}

// 导出数据库服务管理器实例
export const databaseServiceManager = DatabaseServiceManager.getInstance();

// 为了向后兼容，保留 serviceFactory 导出
export const serviceFactory = {
  initialize: () => databaseServiceManager.initialize(),
  isDatabaseAvailable: () => databaseServiceManager.isDatabaseAvailable(),
  getVersionService: () => versionDatabaseService,
  getConfigService: () => configDatabaseService,
  getStatsService: () => statsDatabaseService,
};

// 直接导出数据库服务实例
export const getVersionService = (): IVersionService => versionDatabaseService;
export const getConfigService = (): IConfigService => configDatabaseService;
export const getStatsService = (): IStatsService => statsDatabaseService;

// 兼容性导出（保持现有代码可以正常工作）
export const versionService = versionDatabaseService;
export const configService = configDatabaseService;
export const statsService = statsDatabaseService;
