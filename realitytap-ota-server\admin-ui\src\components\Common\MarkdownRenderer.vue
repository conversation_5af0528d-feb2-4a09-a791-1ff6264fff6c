<template>
  <div
    v-if="renderedHtml"
    class="markdown-content"
    v-html="renderedHtml"
  ></div>
  <div v-else-if="content" class="markdown-content">
    {{ content }}
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

interface Props {
  content: string;
  enableMarkdown?: boolean;
  sanitize?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  enableMarkdown: true,
  sanitize: true,
});

// 配置 marked 选项
const markedOptions = {
  breaks: true, // 支持换行符转换为 <br>
  gfm: true, // 启用 GitHub Flavored Markdown
  headerIds: false, // 禁用标题 ID 生成
  mangle: false, // 禁用邮箱地址混淆
};

// 渲染的 HTML 内容
const renderedHtml = ref<string | null>(null);

// 监听内容变化并异步渲染
watch(
  () => [props.content, props.enableMarkdown, props.sanitize],
  async () => {
    if (!props.content || !props.enableMarkdown) {
      renderedHtml.value = null;
      return;
    }

    try {
      // 使用 marked 解析 Markdown (异步)
      const rawHtml = await marked(props.content, markedOptions);

      // 如果启用了安全清理，使用 DOMPurify 清理 HTML
      if (props.sanitize) {
        renderedHtml.value = DOMPurify.sanitize(rawHtml, {
          ALLOWED_TAGS: [
            'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li',
            'blockquote', 'pre', 'code',
            'a', 'img',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'hr', 'div', 'span'
          ],
          ALLOWED_ATTR: [
            'href', 'title', 'alt', 'src', 'width', 'height',
            'class', 'id', 'style'
          ],
          ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
        });
      } else {
        renderedHtml.value = rawHtml;
      }
    } catch (error) {
      console.warn('Markdown 渲染失败:', error);
      renderedHtml.value = null;
    }
  },
  { immediate: true }
);

// 检测内容是否包含 Markdown 语法
const hasMarkdownSyntax = computed(() => {
  if (!props.content) return false;
  
  // 简单的 Markdown 语法检测
  const markdownPatterns = [
    /^#{1,6}\s+/m, // 标题
    /\*\*.*?\*\*/g, // 粗体
    /\*.*?\*/g, // 斜体
    /`.*?`/g, // 行内代码
    /```[\s\S]*?```/g, // 代码块
    /^\s*[-*+]\s+/m, // 无序列表
    /^\s*\d+\.\s+/m, // 有序列表
    /\[.*?\]\(.*?\)/g, // 链接
    /!\[.*?\]\(.*?\)/g, // 图片
  ];
  
  return markdownPatterns.some(pattern => pattern.test(props.content));
});

// 导出检测函数供外部使用
defineExpose({
  hasMarkdownSyntax,
});
</script>

<style scoped>
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
  color: var(--n-text-color);
}

/* Markdown 样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content :deep(h1) { font-size: 1.5em; }
.markdown-content :deep(h2) { font-size: 1.3em; }
.markdown-content :deep(h3) { font-size: 1.1em; }
.markdown-content :deep(h4) { font-size: 1em; }
.markdown-content :deep(h5) { font-size: 0.9em; }
.markdown-content :deep(h6) { font-size: 0.8em; }

.markdown-content :deep(p) {
  margin: 8px 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-content :deep(li) {
  margin: 4px 0;
}

.markdown-content :deep(blockquote) {
  margin: 8px 0;
  padding: 8px 16px;
  border-left: 4px solid var(--n-border-color);
  background-color: var(--n-color-target);
  color: var(--n-text-color-disabled);
}

.markdown-content :deep(code) {
  padding: 2px 4px;
  background-color: var(--n-color-target);
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  margin: 8px 0;
  padding: 12px;
  background-color: var(--n-color-target);
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-content :deep(pre code) {
  padding: 0;
  background-color: transparent;
}

.markdown-content :deep(a) {
  color: var(--n-primary-color);
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(strong) {
  font-weight: 600;
}

.markdown-content :deep(em) {
  font-style: italic;
}

.markdown-content :deep(hr) {
  margin: 16px 0;
  border: none;
  border-top: 1px solid var(--n-border-color);
}

.markdown-content :deep(table) {
  margin: 8px 0;
  border-collapse: collapse;
  width: 100%;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 8px 12px;
  border: 1px solid var(--n-border-color);
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: var(--n-color-target);
  font-weight: 600;
}

.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}
</style>
