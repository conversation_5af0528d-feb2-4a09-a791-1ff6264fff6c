[package]
name = "realitytap_studio"
version = "1.0.10"
description = "AWA RealityTap Studio Main Application"
authors = ["AWA Customer Support Team"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

# [lib] section removed - not needed for Tauri applications
# Tauri apps are typically standalone executables

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }
chrono = { version = "0.4", features = ["serde"] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.5.0", features = [] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-updater = "2.0.0"
tauri-plugin-process = "2.0.0"
tauri-plugin-os = "2.0.0"
zip = "2.1"
md5 = "0.7"
uuid = { version = "1.8", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-shell = "2"
hound = "3.5"
symphonia = { version = "0.5", features = ["mp3", "isomp4", "aac", "pcm", "vorbis"] }
symphonia-bundle-mp3 = "0.5"
symphonia-core = "0.5"
rustfft = "6.2"
num-complex = "0.4"
async-trait = "0.1"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.12", features = ["json", "stream"] }
sha2 = "0.10"
futures-util = "0.3"
tempfile = "3.8"
once_cell = "1.19"
sysinfo = "0.30"
# C++ library integration dependencies
libloading = "0.8"
libc = "0.2"
parking_lot = "0.12"
crossbeam-channel = "0.5"

# Windows-specific dependencies
[target.'cfg(windows)'.dependencies]
winreg = "0.52"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
# 性能测试功能（仅在开发和测试时启用）
performance-testing = []
