import { config } from '@/config/server.config';
import { FileUtil } from '@/utils/file.util';
import { logger } from '@/utils/logger.util';
import fs from 'fs-extra';
import path from 'path';

/**
 * 版本清理服务
 * 负责清理孤立的元数据文件和维护存储目录
 * 替代原来的 version-sync.service.ts，专注于清理功能
 */
export class VersionCleanupService {
  private readonly versionsMetadataPath: string;
  private readonly releasesPath: string;

  constructor() {
    this.versionsMetadataPath = path.join(config.storage.metadataPath, 'versions');
    this.releasesPath = config.storage.releasesPath;
  }

  /**
   * 初始化存储目录
   */
  async initializeDirectories(): Promise<void> {
    try {
      await FileUtil.ensureDir(this.versionsMetadataPath);
      await FileUtil.ensureDir(this.releasesPath);
      logger.info('Version storage directories initialized');
    } catch (error) {
      logger.error('Failed to initialize version storage directories', { error });
      throw error;
    }
  }

  /**
   * 验证存储目录结构
   * 确保必要的目录存在
   */
  async validateDirectoryStructure(): Promise<void> {
    try {
      const requiredDirs = [
        this.versionsMetadataPath,
        path.join(this.releasesPath, 'stable'),
        path.join(this.releasesPath, 'beta'),
        path.join(this.releasesPath, 'alpha'),
      ];

      for (const dir of requiredDirs) {
        if (!(await FileUtil.exists(dir))) {
          logger.info('Creating missing directory', { dir });
          await FileUtil.ensureDir(dir);
        }
      }

      logger.info('Directory structure validation completed');
    } catch (error) {
      logger.error('Directory structure validation failed', { error });
      throw error;
    }
  }

  /**
   * 清理孤立的元数据文件
   * 删除没有对应发布文件的元数据文件
   */
  async cleanupOrphanedMetadata(): Promise<void> {
    try {
      logger.info('Starting cleanup of orphaned metadata files');

      if (!(await FileUtil.exists(this.versionsMetadataPath))) {
        logger.info('Versions metadata directory does not exist, skipping cleanup');
        return;
      }

      const metadataFiles = await fs.readdir(this.versionsMetadataPath);
      const jsonFiles = metadataFiles.filter(file => file.endsWith('.json'));

      let cleanedCount = 0;

      for (const file of jsonFiles) {
        try {
          const metadataPath = path.join(this.versionsMetadataPath, file);
          const metadata = await fs.readJSON(metadataPath);

          // 检查对应的发布文件是否存在
          const releaseFilePath = await this.findReleaseFile(metadata.filename, metadata.channel);
          
          if (!releaseFilePath) {
            logger.info('Removing orphaned metadata file', { 
              file, 
              filename: metadata.filename,
              channel: metadata.channel 
            });
            await fs.remove(metadataPath);
            cleanedCount++;
          }
        } catch (error) {
          logger.warn('Failed to process metadata file during cleanup', { file, error });
        }
      }

      if (cleanedCount > 0) {
        logger.info('Cleaned up orphaned metadata files', { count: cleanedCount });
      } else {
        logger.info('No orphaned metadata files found');
      }
    } catch (error) {
      logger.error('Failed to cleanup orphaned metadata', { error });
    }
  }

  /**
   * 查找发布文件
   * 在各个渠道目录中查找指定的文件
   */
  private async findReleaseFile(filename: string, channel: string): Promise<string | null> {
    const channelPath = path.join(this.releasesPath, channel);
    const filePath = path.join(channelPath, filename);

    if (await FileUtil.exists(filePath)) {
      return filePath;
    }

    // 如果在指定渠道中找不到，尝试在其他渠道中查找
    const channels = ['stable', 'beta', 'alpha'];
    for (const ch of channels) {
      if (ch === channel) continue; // 跳过已经检查过的渠道
      
      const altPath = path.join(this.releasesPath, ch, filename);
      if (await FileUtil.exists(altPath)) {
        logger.warn('File found in different channel', { 
          filename, 
          expectedChannel: channel, 
          foundChannel: ch 
        });
        return altPath;
      }
    }

    return null;
  }

  /**
   * 清理临时文件
   * 删除上传过程中产生的临时文件
   */
  async cleanupTemporaryFiles(): Promise<void> {
    try {
      const tempPath = config.storage.tempPath || path.join(config.storage.basePath, 'temp');
      
      if (!(await FileUtil.exists(tempPath))) {
        logger.info('Temp directory does not exist, skipping cleanup');
        return;
      }

      const tempFiles = await fs.readdir(tempPath);
      let cleanedCount = 0;

      for (const file of tempFiles) {
        try {
          const filePath = path.join(tempPath, file);
          const stats = await fs.stat(filePath);
          
          // 删除超过24小时的临时文件
          const ageHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
          if (ageHours > 24) {
            await fs.remove(filePath);
            cleanedCount++;
            logger.debug('Removed old temp file', { file, ageHours: ageHours.toFixed(2) });
          }
        } catch (error) {
          logger.warn('Failed to process temp file during cleanup', { file, error });
        }
      }

      if (cleanedCount > 0) {
        logger.info('Cleaned up temporary files', { count: cleanedCount });
      }
    } catch (error) {
      logger.error('Failed to cleanup temporary files', { error });
    }
  }

  /**
   * 执行完整的清理操作
   */
  async performFullCleanup(): Promise<void> {
    try {
      logger.info('Starting full cleanup operation');
      
      await this.validateDirectoryStructure();
      await this.cleanupOrphanedMetadata();
      await this.cleanupTemporaryFiles();
      
      logger.info('Full cleanup operation completed');
    } catch (error) {
      logger.error('Full cleanup operation failed', { error });
      throw error;
    }
  }
}

// 导出单例实例
export const versionCleanupService = new VersionCleanupService();

// 为了向后兼容，也导出为 versionSyncService
export const versionSyncService = {
  validateAndRepairConfig: () => versionCleanupService.validateDirectoryStructure(),
  cleanupOrphanedMetadata: () => versionCleanupService.cleanupOrphanedMetadata(),
  performFullCleanup: () => versionCleanupService.performFullCleanup(),
};
