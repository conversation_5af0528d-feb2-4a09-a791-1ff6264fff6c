<?if $(sys.B<PERSON><PERSON>ARCH)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="RealityTap Haptics Studio"
            UpgradeCode="dfa82347-b28e-549b-8f38-eb7559a97d0a"
            Language="!(loc.TauriLanguage)"
            Manufacturer="AWA"
            Version="1.0.10">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="RealityTap Haptics Studio" Description="Runs RealityTap Haptics Studio" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="RealityTap Haptics Studio"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="RealityTap Haptics Studio"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="0147ef6a-fc30-5fd9-bd41-312c6ae15b1a" Win64="$(var.Win64)">
                <File Id="Path" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\realitytap_studio.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Component Id="Ie598285fb1a24f128be309b74e3797f8" Guid="809c0129-6ca1-4232-bf7d-4d524622b626" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie598285fb1a24f128be309b74e3797f8" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtcore.dll" /></Component><Component Id="I6eda8b9585954086802169b242d1de22" Guid="edce2e1f-fa28-479b-a83d-faa4817598bf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6eda8b9585954086802169b242d1de22" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtssl.dll" /></Component><Component Id="I8a9f4c845597485d93ca76a36d0882fb" Guid="0b8a4e06-f83d-4bba-9ac0-339bd340b962" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8a9f4c845597485d93ca76a36d0882fb" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtutils.dll" /></Component><Component Id="I5670b08779b446a1a7008ab502798df2" Guid="f74964e1-fc2a-4ba1-8664-ff128e781b6b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5670b08779b446a1a7008ab502798df2" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libgcc_s_seh-1.dll" /></Component><Component Id="I7d9dd3cc2135458e8c4dcf5a32ef24d2" Guid="8de19855-3c80-4d39-a886-8c19b5a2865e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7d9dd3cc2135458e8c4dcf5a32ef24d2" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libstdc++-6.dll" /></Component><Component Id="I698777b2d29c4066ba092217a20a9817" Guid="adb91f02-3aa5-4307-ade7-************" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I698777b2d29c4066ba092217a20a9817" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libwinpthread-1.dll" /></Component><Component Id="I3f54476702e94979906d3df4c710aaec" Guid="7cc08482-e733-4b78-a5c4-ffce4a55c69b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I3f54476702e94979906d3df4c710aaec" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffmpeg.exe" /></Component><Component Id="I779da93add86431baaf3d414b2f589df" Guid="c30b27c8-d178-41ca-a1bd-02df56f53f2b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I779da93add86431baaf3d414b2f589df" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffprobe.exe" /></Component><Directory Id="I26b83c0bc81649a68feae1398cb84aa5" Name="motors"><Component Id="Ib5ca1a8f20704f608c315f63ea506d2c" Guid="e9dc3cbf-a157-4112-be73-b2c049353735" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib5ca1a8f20704f608c315f63ea506d2c" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_normal_170Hz.conf" /></Component><Component Id="Ib8f4bae535a34e79be7b3fdc4274fec1" Guid="d9bec486-9fe2-4cc3-b6b1-5000104c9977" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib8f4bae535a34e79be7b3fdc4274fec1" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_pro_170Hz.conf" /></Component><Component Id="Idbff204342eb44668c90448c1943952e" Guid="ff4fc456-2742-4da3-9a61-89db16f63b23" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idbff204342eb44668c90448c1943952e" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0916_normal_170Hz.conf" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall RealityTap Haptics Studio"
						  Description="Uninstalls RealityTap Haptics Studio"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\AWA\RealityTap Haptics Studio"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="RealityTap Haptics Studio"
                    Description="Runs RealityTap Haptics Studio"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.awa.realitytap.desktop"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="Ie598285fb1a24f128be309b74e3797f8"/>
<ComponentRef Id="I6eda8b9585954086802169b242d1de22"/>
<ComponentRef Id="I8a9f4c845597485d93ca76a36d0882fb"/>
<ComponentRef Id="I5670b08779b446a1a7008ab502798df2"/>
<ComponentRef Id="I7d9dd3cc2135458e8c4dcf5a32ef24d2"/>
<ComponentRef Id="I698777b2d29c4066ba092217a20a9817"/>
<ComponentRef Id="I3f54476702e94979906d3df4c710aaec"/>
<ComponentRef Id="I779da93add86431baaf3d414b2f589df"/>
<ComponentRef Id="Ib5ca1a8f20704f608c315f63ea506d2c"/>
<ComponentRef Id="Ib8f4bae535a34e79be7b3fdc4274fec1"/>
<ComponentRef Id="Idbff204342eb44668c90448c1943952e"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
