<template>
  <div class="version-management">
    <n-page-header title="版本管理" subtitle="管理 OTA 更新文件">
      <template #extra>
        <n-space>
          <n-button type="primary" @click="showUploadModal = true">
            <template #icon>
              <n-icon :component="CloudUploadOutline" />
            </template>
            上传文件
          </n-button>

          <n-button type="warning" :loading="cleanupLoading" @click="showCleanupConfirmDialog">
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            清理无效版本
          </n-button>

          <n-button :loading="adminStore.loading" @click="adminStore.fetchVersions()">
            <template #icon>
              <n-icon :component="RefreshOutline" />
            </template>
            刷新
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <!-- 版本列表 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="adminStore.versions"
        :loading="adminStore.loading"
        :pagination="pagination"
        :row-key="(row: VersionInfo) => row.id"
        :scroll-x="1200"
        class="version-table"
      />
    </n-card>

    <!-- 上传文件模态框 -->
    <n-modal
      v-model:show="showUploadModal"
      preset="card"
      title="上传版本文件"
      style="width: 720px"
      :mask-closable="false"
    >
      <chunk-file-upload @success="handleUploadSuccess" @cancel="showUploadModal = false" />
    </n-modal>

    <!-- 删除确认对话框 -->
    <n-modal
      v-model:show="showDeleteModal"
      preset="dialog"
      type="warning"
      title="确认删除"
      positive-text="删除"
      negative-text="取消"
      @positive-click="handleConfirmDelete"
      @negative-click="showDeleteModal = false"
    >
      <div v-if="deleteTarget">
        <p><strong>确定要删除以下文件吗？此操作不可恢复。</strong></p>
        <div style="margin: 16px 0; padding: 12px; background-color: var(--n-color-target); border-radius: 6px;">
          <p><strong>版本：</strong>{{ deleteTarget.version }}</p>
          <p><strong>安装包：</strong>{{ deleteTarget.filename }}</p>
          <p v-if="deleteTarget.hasSignature && deleteTarget.signatureFilename">
            <strong>签名文件：</strong>{{ deleteTarget.signatureFilename }}
          </p>
          <p v-if="!deleteTarget.hasSignature" style="color: var(--n-text-color-disabled);">
            <strong>签名文件：</strong>无
          </p>
          <p v-if="deleteTarget.hasHashFile && deleteTarget.hashFilename">
            <strong>哈希文件：</strong>{{ deleteTarget.hashFilename }}
          </p>
          <p v-if="!deleteTarget.hasHashFile" style="color: var(--n-text-color-disabled);">
            <strong>哈希文件：</strong>无
          </p>
        </div>
        <p style="color: var(--n-warning-color);">
          <strong>注意：</strong>{{ getDeleteWarningMessage(deleteTarget) }}
        </p>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import type { VersionInfo } from '@/api/types';
import ChunkFileUpload from '@/components/Common/ChunkFileUpload.vue';
import MarkdownRenderer from '@/components/Common/MarkdownRenderer.vue';
import { useAdminStore } from '@/stores/admin';
import { CloudUploadOutline, DownloadOutline, RefreshOutline, TrashOutline, CheckmarkCircleOutline, CloseCircleOutline, AlertCircleOutline, DocumentTextOutline } from '@vicons/ionicons5';
import {
  NButton,
  NCard,
  NDataTable,
  NIcon,
  NModal,
  NPageHeader,
  NSpace,
  NSwitch,
  NTag,
  NTooltip,
  useMessage,
  useDialog,
  type DataTableColumns,
} from 'naive-ui';
import { h, onMounted, ref } from 'vue';

const message = useMessage();
const dialog = useDialog();
const adminStore = useAdminStore();

const showUploadModal = ref(false);
const showDeleteModal = ref(false);
const deleteTarget = ref<VersionInfo | null>(null);
const cleanupLoading = ref(false);

// 分页配置
const pagination = {
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
};

// 表格列定义
const columns: DataTableColumns<VersionInfo> = [
  {
    title: '版本',
    key: 'version',
    width: 120,
    sorter: 'default',
  },
  {
    title: '平台',
    key: 'platform',
    width: 100,
    render: row => {
      const colorMap: Record<string, 'default' | 'error' | 'primary' | 'success' | 'info' | 'warning'> = {
        windows: 'info',
        macos: 'success',
        linux: 'warning',
      };
      return h(NTag, { type: colorMap[row.platform] || 'default' }, () => row.platform);
    },
  },
  {
    title: '架构',
    key: 'architecture',
    width: 100,
  },
  {
    title: '渠道',
    key: 'channel',
    width: 100,
    render: row => {
      const colorMap: Record<string, 'default' | 'error' | 'primary' | 'success' | 'info' | 'warning'> = {
        stable: 'success',
        beta: 'warning',
        alpha: 'error',
      };
      return h(NTag, { type: colorMap[row.channel] || 'default' }, () => row.channel);
    },
  },
  {
    title: '文件名',
    key: 'filename',
    minWidth: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '文件大小',
    key: 'fileSize',
    width: 120,
    render: row => formatBytes(row.fileSize),
    sorter: (a, b) => a.fileSize - b.fileSize,
  },
  {
    title: '下载次数',
    key: 'downloadCount',
    width: 100,
    sorter: (a, b) => a.downloadCount - b.downloadCount,
  },
  {
    title: '更新内容',
    key: 'releaseNotes',
    width: 100,
    align: 'center',
    render: row => {
      if (row.releaseNotes && row.releaseNotes.trim()) {
        return h(NTooltip, {
          trigger: 'hover',
          style: { maxWidth: '500px' },
          placement: 'top'
        }, {
          trigger: () => h(NIcon, {
            component: DocumentTextOutline,
            size: 18,
            style: {
              color: 'var(--n-primary-color)',
              cursor: 'pointer'
            }
          }),
          default: () => h('div', {
            style: {
              maxHeight: '300px',
              overflowY: 'auto',
              padding: '8px 0'
            }
          }, [
            h(MarkdownRenderer, {
              content: row.releaseNotes,
              enableMarkdown: true,
              sanitize: true
            })
          ])
        });
      } else {
        return h('span', {
          style: {
            color: 'var(--n-text-color-disabled)',
            fontSize: '12px'
          }
        }, '无');
      }
    },
  },
  {
    title: '签名状态',
    key: 'hasSignature',
    width: 100,
    render: row => {
      if (row.hasSignature) {
        return h(NTooltip, { trigger: 'hover', style: { maxWidth: '400px' } }, {
          trigger: () => h(NTag, { type: 'success', size: 'small' }, {
            icon: () => h(NIcon, { component: CheckmarkCircleOutline }),
            default: () => '已签名'
          }),
          default: () => {
            if (row.signature) {
              // 显示签名值的前100个字符
              const signaturePreview = row.signature.length > 100
                ? row.signature.substring(0, 100) + '...'
                : row.signature;
              return `签名内容: ${signaturePreview}`;
            } else if (row.signatureFilename) {
              return `签名文件: ${row.signatureFilename}`;
            } else {
              return '已签名';
            }
          }
        });
      } else {
        return h(NTag, { type: 'default', size: 'small' }, {
          icon: () => h(NIcon, { component: CloseCircleOutline }),
          default: () => '未签名'
        });
      }
    },
  },
  {
    title: '哈希文件',
    key: 'hasHashFile',
    width: 100,
    render: row => {
      if (row.hasHashFile) {
        return h(NTooltip, { trigger: 'hover', style: { maxWidth: '400px' } }, {
          trigger: () => h(NTag, { type: 'info', size: 'small' }, {
            icon: () => h(NIcon, { component: CheckmarkCircleOutline }),
            default: () => '有哈希'
          }),
          default: () => {
            if (row.checksum && row.checksum.trim()) {
              // 显示实际的hash值
              return h('div', {
                style: {
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  wordBreak: 'break-all',
                  padding: '4px 0'
                }
              }, `哈希值: ${row.checksum}`);
            } else {
              // 回退到显示文件名
              return row.hashFilename ? `哈希文件: ${row.hashFilename}` : '有哈希文件';
            }
          }
        });
      } else {
        return h(NTag, { type: 'default', size: 'small' }, {
          icon: () => h(NIcon, { component: CloseCircleOutline }),
          default: () => '无哈希'
        });
      }
    },
  },
  {
    title: '强制更新',
    key: 'isForced',
    width: 120,
    render: row => {
      return h(NTooltip, { trigger: 'hover' }, {
        trigger: () => h(NSwitch, {
          value: row.isForced,
          loading: row.updating,
          checkedValue: true,
          uncheckedValue: false,
          onUpdateValue: (value: boolean) => handleToggleForceUpdate(row, value),
        }),
        default: () => row.isForced ? '此版本为强制更新，用户必须更新到此版本' : '此版本为可选更新，用户可以选择是否更新'
      });
    },
  },
  {
    title: '上传时间',
    key: 'uploadTime',
    width: 180,
    render: row => new Date(row.uploadTime).toLocaleString(),
    sorter: (a, b) => new Date(a.uploadTime).getTime() - new Date(b.uploadTime).getTime(),
  },
  {
    title: '操作',
    key: 'actions',
    width: 160,
    render: row => {
      const actions = [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            quaternary: true,
            onClick: () => handleDownload(row),
          },
          {
            icon: () => h(NIcon, { component: DownloadOutline }),
          },
        ),
      ];

      actions.push(
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            quaternary: true,
            onClick: () => handleDelete(row),
          },
          {
            icon: () => h(NIcon, { component: TrashOutline }),
          },
        )
      );

      return h(NSpace, { size: 'small' }, () => actions);
    },
  },
];

// 格式化字节数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取删除警告消息
const getDeleteWarningMessage = (version: VersionInfo): string => {
  const filesToDelete = ['安装包'];

  if (version.hasSignature) {
    filesToDelete.push('签名文件');
  }

  if (version.hasHashFile) {
    filesToDelete.push('哈希文件');
  }

  return `${filesToDelete.join('、')}都将被永久删除。`;
};

// 处理文件下载
const handleDownload = (version: VersionInfo) => {
  try {
    const downloadUrl = `/api/v1/download/${encodeURIComponent(version.filename)}`;
    console.log('开始下载文件:', {
      filename: version.filename,
      downloadUrl,
      encodedFilename: encodeURIComponent(version.filename),
    });

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = version.filename; // 设置下载文件名
    link.style.display = 'none';

    // 添加到DOM并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);

    message.success('开始下载文件');

    // 记录下载事件
    console.log('下载链接已触发:', {
      href: link.href,
      download: link.download,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('下载失败:', error);
    message.error(`下载失败: ${error.message}`);

    // 如果程序化下载失败，回退到 window.open
    try {
      const downloadUrl = `/api/v1/download/${encodeURIComponent(version.filename)}`;
      window.open(downloadUrl, '_blank');
      message.info('已在新窗口中打开下载链接');
    } catch (fallbackError) {
      console.error('回退下载方法也失败:', fallbackError);
    }
  }
};

// 处理删除版本
const handleDelete = (version: VersionInfo) => {
  deleteTarget.value = version;
  showDeleteModal.value = true;
};

// 确认删除
const handleConfirmDelete = async () => {
  if (!deleteTarget.value) return;

  const result = await adminStore.deleteVersion(deleteTarget.value.id);

  if (result.success) {
    // 根据删除的文件类型显示不同的成功消息
    const deletedFiles = ['版本'];
    if (deleteTarget.value.hasSignature) {
      deletedFiles.push('签名文件');
    }
    if (deleteTarget.value.hasHashFile) {
      deletedFiles.push('哈希文件');
    }

    message.success(`${deletedFiles.join('、')}删除成功`);
  } else {
    message.error(result.message || '删除失败');
  }

  showDeleteModal.value = false;
  deleteTarget.value = null;
};

// 处理上传成功
const handleUploadSuccess = () => {
  showUploadModal.value = false;
  message.success('文件上传成功');
  adminStore.fetchVersions();
};

// 显示清理确认对话框
const showCleanupConfirmDialog = () => {
  dialog.warning({
    title: '确认清理无效版本',
    content: '此操作将删除数据库中存在但实际安装包文件已不存在的版本记录。这个操作不可恢复，确定要继续吗？',
    positiveText: '确定清理',
    negativeText: '取消',
    onPositiveClick: handleCleanupInvalidVersions,
  });
};

// 处理强制更新切换
const handleToggleForceUpdate = async (version: VersionInfo, isForced: boolean) => {
  // 设置更新状态
  version.updating = true;

  try {
    const response = await adminStore.toggleForceUpdate(version.filename, isForced);

    if (response.success) {
      // 更新本地数据
      version.isForced = isForced;
      message.success(isForced ? '已设置为强制更新' : '已设置为可选更新');
    } else {
      message.error(response.message || '更新失败');
    }
  } catch (error: any) {
    console.error('切换强制更新状态失败:', error);
    message.error(`更新失败: ${error.message || '未知错误'}`);
  } finally {
    // 清除更新状态
    version.updating = false;
  }
};

// 清理无效版本
const handleCleanupInvalidVersions = async () => {
  cleanupLoading.value = true;
  try {
    const response = await adminStore.cleanupInvalidVersions();
    if (response.success && 'data' in response && response.data) {
      const { totalFound, totalCleaned, cleanedVersions } = response.data;

      if (totalFound === 0) {
        message.info('未发现无效版本');
      } else if (totalCleaned === 0) {
        message.warning(`发现 ${totalFound} 个无效版本，但清理失败`);
      } else {
        message.success(`成功清理 ${totalCleaned} 个无效版本`);

        // 显示详细信息对话框
        if (cleanedVersions.length > 0) {
          const details = cleanedVersions.map((v: { filename: string; reason: string }) => `• ${v.filename} (${v.reason})`).join('\n');

          dialog.success({
            title: '清理完成',
            content: () => h('div', { style: 'white-space: pre-line;' }, `成功清理了 ${totalCleaned} 个无效版本：\n\n${details}`),
            positiveText: '确定',
          });

          console.log('清理的无效版本详情:\n' + details);
        }

        // 刷新版本列表
        await adminStore.fetchVersions();
      }
    } else {
      const errorMessage = 'message' in response ? response.message : '清理失败';
      message.error(errorMessage || '清理失败');
    }
  } catch (error: any) {
    console.error('清理无效版本失败:', error);
    message.error(`清理失败: ${error.message || '未知错误'}`);
  } finally {
    cleanupLoading.value = false;
  }
};

onMounted(() => {
  adminStore.fetchVersions();
});
</script>

<style scoped>
.version-management {
  min-width: 1000px;
  margin: 0 auto;
}

/* 表格样式优化 */
.version-table {
  width: 100%;
}

/* 确保表格容器占满可用宽度 */
.version-table :deep(.n-data-table) {
  width: 100%;
}

.version-table :deep(.n-data-table-wrapper) {
  width: 100%;
}

.version-table :deep(.n-data-table-table) {
  width: 100%;
  table-layout: fixed;
}
</style>
