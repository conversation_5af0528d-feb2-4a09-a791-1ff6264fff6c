{"name": "@realitytap/ota-admin-ui", "version": "1.0.0", "description": "RealityTap OTA Server Admin Interface", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@types/dompurify": "^3.0.5", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "dompurify": "^3.2.6", "marked": "^16.1.2", "naive-ui": "^2.38.1", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "typescript": "^5.3.3", "vite": "^6.3.5", "vue-tsc": "^3.0.0-alpha.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}