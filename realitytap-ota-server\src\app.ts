import { config } from '@/config/server.config';
import { corsMiddleware } from '@/middleware/cors.middleware';
import { errorMiddleware } from '@/middleware/error.middleware';
import { adminRateLimitMiddleware, publicRateLimitMiddleware } from '@/middleware/rate-limit.middleware';
import { ipLoggingMiddleware } from '@/middleware/ip-logging.middleware';
import { logger } from '@/utils/logger.util';
import compression from 'compression';
import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import path from 'path';

// Import controllers
import { adminController } from '@/controllers/admin.controller';
import { configController } from '@/controllers/config.controller';
import { downloadController } from '@/controllers/download.controller';
import { healthController } from '@/controllers/health.controller';
import { statsController } from '@/controllers/stats.controller';
import updaterController from '@/controllers/updater.controller';
import { versionController } from '@/controllers/version.controller';

class OTAServer {
  private app: express.Application;
  private port: number;
  private authFailureCache = new Map<string, number>();

  constructor() {
    this.app = express();
    this.port = config.server.port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();

    // 清理认证失败缓存
    setInterval(() => {
      const now = Date.now();
      for (const [key, timestamp] of this.authFailureCache.entries()) {
        if (now - timestamp > 60000) {
          // 1分钟
          this.authFailureCache.delete(key);
        }
      }
    }, 60000);
  }

  private initializeMiddleware(): void {
    // Trust proxy for rate limiting (to get real IP from headers)
    this.app.set('trust proxy', true);

    // Security middleware
    this.app.use(
      helmet({
        crossOriginResourcePolicy: { policy: 'cross-origin' },
        contentSecurityPolicy: false, // Disable for file downloads
      }),
    );

    // CORS middleware
    this.app.use(corsMiddleware);

    // Compression middleware
    this.app.use(compression());

    // IP logging middleware (for OTA requests)
    this.app.use(ipLoggingMiddleware);

    // Request logging with filtering
    this.app.use(
      morgan('combined', {
        stream: {
          write: (message: string) => logger.info(message.trim()),
        },
        skip: (req, res) => {
          // 获取客户端IP信息
          const clientIP = req.ip || req.connection.remoteAddress || '';
          const isPrivateIP = this.isPrivateIP(clientIP);

          // 第一优先级：始终记录的重要条件（安全相关）
          const alwaysLogConditions = [
            // 安全相关：认证失败
            res.statusCode === 401 || res.statusCode === 403,
            // 安全相关：服务器错误
            res.statusCode >= 500,
            // 安全相关：管理员重要操作（非GET请求）
            req.url.includes('/admin/') && req.method !== 'GET' && (
              req.url.includes('/upload') ||
              req.url.includes('/delete') ||
              req.url.includes('/login') ||
              req.url.includes('/logout')
            ),
          ];

          // 如果满足始终记录的条件，不跳过
          if (alwaysLogConditions.some(condition => condition)) {
            // 但仍然跳过重复的401认证失败请求（防止日志轰炸）
            if (res.statusCode === 401 && this.shouldSkipAuthFailureLog(req)) {
              return true;
            }
            return false;
          }

          // 第二优先级：过滤不必要的请求
          const alwaysSkipConditions = [
            // 静态资源请求
            req.url.startsWith('/admin/assets/'),
            // favicon请求
            req.url === '/favicon.ico' || req.url.includes('favicon'),
            // 健康检查请求
            req.url === '/health' || req.url === '/ping',
            // 管理界面的GET请求（查看类操作）
            req.method === 'GET' && req.url.includes('/admin/') && (
              req.url.includes('/logs') ||           // 日志查看请求
              req.url.includes('/versions') ||       // 版本列表请求
              req.url.includes('/health') ||         // 健康检查请求
              req.url.includes('/stats') ||          // 统计信息请求
              req.url.includes('/config')            // 配置查看请求
            ),
          ];

          if (alwaysSkipConditions.some(condition => condition)) {
            return true; // 跳过这些请求
          }

          // 第三优先级：记录重要的业务请求（OTA功能和版本管理）
          const importantBusinessRequests = [
            // OTA相关的客户端请求
            req.url.includes('/api/v1/version') ||
            req.url.includes('/api/v1/download') ||
            req.url.includes('/api/v1/updates'),
            // 远程客户端的重要请求
            !isPrivateIP && (
              req.url.includes('/api/v1/download') ||
              req.url.includes('/api/v1/version')
            ),
          ];

          // 如果是重要的业务请求，记录日志
          if (importantBusinessRequests.some(condition => condition)) {
            return false;
          }

          // 其他所有请求都跳过（包括内网的管理界面操作）
          return true;
        },
      }),
    );

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  private initializeRoutes(): void {
    // Health check routes (no rate limiting)
    this.app.use('/health', healthController);
    this.app.use('/api/v1/health', healthController);

    // 管理API路由 (使用管理API速率限制)
    this.app.use('/api/v1/admin', adminRateLimitMiddleware, adminController);
    this.app.use('/api/v1/stats', adminRateLimitMiddleware, statsController);
    this.app.use('/api/v1/config', adminRateLimitMiddleware, configController);

    // 公共API路由 (使用公共API速率限制)
    this.app.use('/api/v1/version', publicRateLimitMiddleware, versionController);
    this.app.use('/api/v1/download', publicRateLimitMiddleware, downloadController);
    this.app.use('/api/v1/updates', publicRateLimitMiddleware, updaterController);

    // Serve favicon.ico from root directory
    this.app.get('/favicon.ico', (req, res) => {
      const faviconPath = path.join(__dirname, '../favicon.ico');
      res.sendFile(faviconPath, (err) => {
        if (err) {
          logger.warn('Favicon not found:', faviconPath);
          res.status(404).end();
        }
      });
    });

    // Static files for admin UI
    // In development mode, use admin-ui/dist; in production, use dist/admin-ui
    const isDevelopment = process.env.NODE_ENV === 'development';
    const adminUIPath = isDevelopment ? path.join(__dirname, '../admin-ui/dist') : path.join(__dirname, 'admin-ui');

    // Check if admin UI files exist
    if (!require('fs').existsSync(adminUIPath)) {
      logger.warn(`Admin UI files not found at: ${adminUIPath}`);
      logger.warn('Please run "npm run build" to build the admin interface');
    }

    this.app.use(
      '/admin',
      express.static(adminUIPath, {
        maxAge: isDevelopment ? 0 : '1d', // Cache for 1 day in production
        etag: true,
        lastModified: true,
      }),
    );

    // Admin UI SPA fallback
    this.app.get('/admin/*', (req, res) => {
      const indexPath = path.join(adminUIPath, 'index.html');
      if (require('fs').existsSync(indexPath)) {
        res.sendFile(indexPath);
      } else {
        res.status(404).json({
          success: false,
          error: {
            code: 'ADMIN_UI_NOT_FOUND',
            message: 'Admin interface not available. Please build the admin UI first.',
          },
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        });
      }
    });

    // Root route - redirect to admin UI
    this.app.get('/', (req, res) => {
      res.redirect('/admin');
    });

    // API status endpoint
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'RealityTap OTA Server',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
      });
    });

    // 404 handler for API routes
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'API endpoint not found',
        },
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      });
    });

    // Fallback for all other routes - serve admin UI
    this.app.use('*', (req, res) => {
      res.sendFile(path.join(adminUIPath, 'index.html'));
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorMiddleware);
  }

  /**
   * 检查是否为私有IP地址
   */
  private isPrivateIP(ip: string): boolean {
    if (!ip) return true; // 无IP视为私有

    // IPv4 私有地址范围
    const privateRanges = [
      /^127\./,                    // *********/8 (localhost)
      /^10\./,                     // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
      /^192\.168\./,               // ***********/16
      /^169\.254\./,               // ***********/16 (link-local)
      /^::1$/,                     // IPv6 localhost
      /^fe80:/,                    // IPv6 link-local
    ];

    return privateRanges.some(range => range.test(ip));
  }

  /**
   * 检查是否应该跳过认证失败的日志记录
   * 防止短时间内相同IP的相同路径重复记录401错误
   */
  private shouldSkipAuthFailureLog(req: express.Request): boolean {
    const cacheKey = `${req.ip}:${req.path}`;
    const now = Date.now();

    if (this.authFailureCache.has(cacheKey)) {
      const lastLogged = this.authFailureCache.get(cacheKey)!;
      if (now - lastLogged < 60000) {
        // 1分钟内
        return true; // 跳过日志
      }
    }

    this.authFailureCache.set(cacheKey, now);
    return false; // 记录日志
  }

  public async start(): Promise<void> {
    // Initialize services
    await this.initializeServices();

    this.app.listen(this.port, config.server.host, () => {
      logger.info(`🚀 RealityTap OTA Server started on ${config.server.host}:${this.port}`);
      logger.info(`📁 Storage path: ${config.storage.basePath}`);
      logger.info(`🔧 Environment: ${config.server.nodeEnv}`);
      logger.info(`📊 Log level: ${config.logging.level}`);
    });

    // Graceful shutdown
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGINT', this.gracefulShutdown.bind(this));
  }

  private async initializeServices(): Promise<void> {
    try {
      // Initialize directories first
      const { initializeDirectories } = await import('@/utils/init-directories.util');
      await initializeDirectories();
      logger.info('📁 Directory structure initialized');

      // Initialize service factory and database services
      const { serviceFactory } = await import('@/services/service-factory');
      await serviceFactory.initialize();
      logger.info('📊 Database services initialized');

      // Initialize version cleanup service
      const { versionCleanupService } = await import('@/services/version-cleanup.service');
      await versionCleanupService.validateDirectoryStructure();
      await versionCleanupService.cleanupOrphanedMetadata();
      logger.info('🧹 Version cleanup service initialized');
    } catch (error) {
      logger.error('Failed to initialize services', { error });
      throw error;
    }
  }

  private gracefulShutdown(signal: string): void {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    // Close server and cleanup resources
    process.exit(0);
  }

  public getApp(): express.Application {
    return this.app;
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new OTAServer();
  server.start().catch(error => {
    logger.error('Failed to start server', { error });
    process.exit(1);
  });
}

export { OTAServer };
